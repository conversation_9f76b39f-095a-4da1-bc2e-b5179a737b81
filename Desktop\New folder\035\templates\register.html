{% extends "base.html" %}

{% block title %}Register - Interactive Experience{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="game-container p-5">
            <h2 class="text-center mb-4">
                <i class="fas fa-user-plus me-2"></i>Create Account
            </h2>
            
            <div class="adult-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Age Verification Required</strong><br>
                You must be 18 years or older to access this platform. 
                By registering, you confirm that you meet this requirement.
            </div>
            
            <form id="registerForm">
                <div class="mb-3">
                    <label for="username" class="form-label">
                        <i class="fas fa-user me-1"></i>Username
                    </label>
                    <input type="text" class="form-control" id="username" name="username" required>
                    <div class="form-text">Choose a unique username (3-20 characters)</div>
                </div>
                
                <div class="mb-3">
                    <label for="email" class="form-label">
                        <i class="fas fa-envelope me-1"></i>Email Address
                    </label>
                    <input type="email" class="form-control" id="email" name="email" required>
                    <div class="form-text">We'll never share your email with anyone else</div>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock me-1"></i>Password
                    </label>
                    <input type="password" class="form-control" id="password" name="password" required>
                    <div class="form-text">Minimum 8 characters with letters and numbers</div>
                </div>
                
                <div class="mb-3">
                    <label for="confirmPassword" class="form-label">
                        <i class="fas fa-lock me-1"></i>Confirm Password
                    </label>
                    <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
                </div>
                
                <div class="mb-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="ageConfirmation" required>
                        <label class="form-check-label" for="ageConfirmation">
                            <strong>I confirm that I am 18 years of age or older</strong>
                        </label>
                    </div>
                </div>
                
                <div class="mb-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="termsAcceptance" required>
                        <label class="form-check-label" for="termsAcceptance">
                            I agree to the <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">Terms of Service</a> 
                            and <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">Privacy Policy</a>
                        </label>
                    </div>
                </div>
                
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-user-plus me-2"></i>Create Account
                        <div class="loading-spinner spinner-border spinner-border-sm ms-2" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </button>
                </div>
            </form>
            
            <div class="text-center mt-4">
                <p class="mb-0">Already have an account?</p>
                <a href="{{ url_for('login') }}" class="btn btn-outline-primary">
                    <i class="fas fa-sign-in-alt me-1"></i>Sign In
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Terms Modal -->
<div class="modal fade" id="termsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Terms of Service</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>1. Acceptance of Terms</h6>
                <p>By accessing this platform, you agree to be bound by these terms and conditions.</p>
                
                <h6>2. Age Requirement</h6>
                <p>You must be at least 18 years old to use this service. By registering, you confirm you meet this requirement.</p>
                
                <h6>3. Content Guidelines</h6>
                <p>This platform contains mature content intended for adult audiences. User discretion is advised.</p>
                
                <h6>4. User Conduct</h6>
                <p>Users must maintain respectful behavior and comply with all applicable laws and regulations.</p>
                
                <h6>5. Privacy and Data</h6>
                <p>We are committed to protecting your privacy. Please review our Privacy Policy for details.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Privacy Modal -->
<div class="modal fade" id="privacyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Privacy Policy</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Information We Collect</h6>
                <p>We collect only the information necessary to provide our services, including account details and usage data.</p>
                
                <h6>How We Use Your Information</h6>
                <p>Your information is used to personalize your experience and improve our services.</p>
                
                <h6>Data Security</h6>
                <p>We implement industry-standard security measures to protect your personal information.</p>
                
                <h6>Third-Party Sharing</h6>
                <p>We do not sell or share your personal information with third parties without your consent.</p>
                
                <h6>Your Rights</h6>
                <p>You have the right to access, modify, or delete your personal information at any time.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.getElementById('registerForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const password = formData.get('password');
        const confirmPassword = formData.get('confirmPassword');
        const ageConfirmed = document.getElementById('ageConfirmation').checked;
        const termsAccepted = document.getElementById('termsAcceptance').checked;
        
        // Validation
        if (password !== confirmPassword) {
            alert('Passwords do not match');
            return;
        }
        
        if (password.length < 8) {
            alert('Password must be at least 8 characters long');
            return;
        }
        
        if (!ageConfirmed) {
            alert('You must confirm that you are 18 years or older');
            return;
        }
        
        if (!termsAccepted) {
            alert('You must accept the Terms of Service and Privacy Policy');
            return;
        }
        
        showLoading(true);
        
        try {
            const response = await fetch('/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: formData.get('username'),
                    email: formData.get('email'),
                    password: password,
                    age_confirmed: ageConfirmed
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                showStatus('Account created successfully!', 'success');
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 1000);
            } else {
                alert(data.error || 'Registration failed');
            }
        } catch (error) {
            console.error('Registration error:', error);
            alert('An error occurred during registration');
        } finally {
            showLoading(false);
        }
    });
    
    // Real-time password validation
    document.getElementById('password').addEventListener('input', function() {
        const password = this.value;
        const feedback = this.nextElementSibling;
        
        if (password.length < 8) {
            feedback.textContent = 'Password too short (minimum 8 characters)';
            feedback.className = 'form-text text-danger';
        } else if (!/(?=.*[a-zA-Z])(?=.*\d)/.test(password)) {
            feedback.textContent = 'Password must contain both letters and numbers';
            feedback.className = 'form-text text-warning';
        } else {
            feedback.textContent = 'Password strength: Good';
            feedback.className = 'form-text text-success';
        }
    });
    
    // Confirm password validation
    document.getElementById('confirmPassword').addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const confirmPassword = this.value;
        
        if (confirmPassword && password !== confirmPassword) {
            this.setCustomValidity('Passwords do not match');
            this.classList.add('is-invalid');
        } else {
            this.setCustomValidity('');
            this.classList.remove('is-invalid');
        }
    });
</script>
{% endblock %}

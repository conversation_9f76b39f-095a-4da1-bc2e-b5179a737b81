<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Interactive Experience{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .game-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }
        
        .choice-btn {
            transition: all 0.3s ease;
            border: 2px solid transparent;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
            padding: 15px 25px;
            margin: 10px 0;
            width: 100%;
            text-align: left;
        }
        
        .choice-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            border-color: #fff;
            color: white;
        }
        
        .scene-content {
            background: rgba(248, 249, 250, 0.8);
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        
        .adult-warning {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
        }
        
        .navbar-custom {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .loading-spinner {
            display: none;
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 25px;
            background: rgba(40, 167, 69, 0.9);
            color: white;
            font-size: 14px;
            z-index: 1000;
            display: none;
        }
        
        .chat-container {
            max-height: 300px;
            overflow-y: auto;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .message {
            margin: 10px 0;
            padding: 8px 12px;
            border-radius: 15px;
            max-width: 80%;
        }
        
        .message.user {
            background: #667eea;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        
        .message.system {
            background: #e9ecef;
            color: #495057;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-gamepad me-2"></i>Interactive Experience
            </a>
            <div class="navbar-nav ms-auto">
                {% if current_user.is_authenticated %}
                    <span class="navbar-text me-3">
                        <i class="fas fa-user me-1"></i>{{ current_user.username }}
                    </span>
                    <a class="nav-link" href="{{ url_for('logout') }}">
                        <i class="fas fa-sign-out-alt me-1"></i>Logout
                    </a>
                {% else %}
                    <a class="nav-link" href="{{ url_for('login') }}">
                        <i class="fas fa-sign-in-alt me-1"></i>Login
                    </a>
                    <a class="nav-link" href="{{ url_for('register') }}">
                        <i class="fas fa-user-plus me-1"></i>Register
                    </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="status-indicator" id="statusIndicator">
        <i class="fas fa-wifi me-2"></i>Connected
    </div>

    <div class="container mt-4">
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script>
        // Global JavaScript functionality
        let socket;
        
        function initializeSocket() {
            if (typeof io !== 'undefined') {
                socket = io();
                
                socket.on('connect', function() {
                    showStatus('Connected', 'success');
                });
                
                socket.on('disconnect', function() {
                    showStatus('Disconnected', 'danger');
                });
                
                socket.on('status', function(data) {
                    console.log('Status:', data.message);
                });
                
                socket.on('game_response', function(data) {
                    console.log('Game response:', data);
                    handleGameResponse(data);
                });
            }
        }
        
        function showStatus(message, type = 'success') {
            const indicator = document.getElementById('statusIndicator');
            indicator.textContent = message;
            indicator.className = `status-indicator alert-${type}`;
            indicator.style.display = 'block';
            
            setTimeout(() => {
                indicator.style.display = 'none';
            }, 3000);
        }
        
        function showLoading(show = true) {
            const spinner = document.querySelector('.loading-spinner');
            if (spinner) {
                spinner.style.display = show ? 'block' : 'none';
            }
        }
        
        function handleGameResponse(data) {
            // Override this in specific pages
            console.log('Game response received:', data);
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeSocket();
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>

{% extends "base.html" %}

{% block title %}Welcome - Interactive Experience{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="game-container p-5 text-center">
            <h1 class="display-4 mb-4">
                <i class="fas fa-star me-3"></i>
                Interactive Experience
            </h1>
            
            <p class="lead mb-4">
                Embark on a sophisticated interactive journey where your choices shape the narrative. 
                Experience rich storytelling with meaningful consequences in a mature, refined environment.
            </p>
            
            <div class="row mt-5">
                <div class="col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-route fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">Choice-Driven Narrative</h5>
                            <p class="card-text">
                                Every decision matters. Navigate through branching storylines 
                                where your choices create unique experiences.
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-users fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">Interactive Community</h5>
                            <p class="card-text">
                                Connect with other players in real-time. Share experiences 
                                and discover new perspectives.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">Secure & Private</h5>
                            <p class="card-text">
                                Your privacy is paramount. Enjoy a secure environment 
                                with robust age verification and content controls.
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 mb-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-mobile-alt fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">Cross-Platform</h5>
                            <p class="card-text">
                                Access your experience from any device. Seamless 
                                synchronization across all platforms.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-5">
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Age Verification Required:</strong> 
                    This platform contains mature content intended for adults only. 
                    You must be 18 years or older to participate.
                </div>
            </div>
            
            <div class="mt-4">
                <a href="{{ url_for('register') }}" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-user-plus me-2"></i>Get Started
                </a>
                <a href="{{ url_for('login') }}" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-sign-in-alt me-2"></i>Sign In
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center mt-5">
    <div class="col-lg-8">
        <div class="game-container p-4">
            <h3 class="text-center mb-4">
                <i class="fas fa-info-circle me-2"></i>Platform Features
            </h3>
            
            <div class="row">
                <div class="col-md-4 text-center mb-3">
                    <i class="fas fa-book-open fa-2x text-primary mb-2"></i>
                    <h6>Rich Storytelling</h6>
                    <small class="text-muted">Immersive narratives with depth and sophistication</small>
                </div>
                
                <div class="col-md-4 text-center mb-3">
                    <i class="fas fa-cogs fa-2x text-primary mb-2"></i>
                    <h6>Customizable Experience</h6>
                    <small class="text-muted">Tailor the content to your preferences</small>
                </div>
                
                <div class="col-md-4 text-center mb-3">
                    <i class="fas fa-chart-line fa-2x text-primary mb-2"></i>
                    <h6>Progress Tracking</h6>
                    <small class="text-muted">Monitor your journey and achievements</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add some interactive elements to the landing page
    document.addEventListener('DOMContentLoaded', function() {
        // Animate cards on scroll
        const cards = document.querySelectorAll('.card');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        });
        
        cards.forEach(card => {
            observer.observe(card);
        });
        
        // Add hover effects
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.transition = 'transform 0.3s ease';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    });
</script>
{% endblock %}

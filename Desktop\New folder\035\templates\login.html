{% extends "base.html" %}

{% block title %}Sign In - Interactive Experience{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-5">
        <div class="game-container p-5">
            <h2 class="text-center mb-4">
                <i class="fas fa-sign-in-alt me-2"></i>Welcome Back
            </h2>
            
            <p class="text-center text-muted mb-4">
                Sign in to continue your interactive experience
            </p>
            
            <form id="loginForm">
                <div class="mb-3">
                    <label for="username" class="form-label">
                        <i class="fas fa-user me-1"></i>Username
                    </label>
                    <input type="text" class="form-control" id="username" name="username" required>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock me-1"></i>Password
                    </label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="rememberMe">
                    <label class="form-check-label" for="rememberMe">
                        Remember me
                    </label>
                </div>
                
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>Sign In
                        <div class="loading-spinner spinner-border spinner-border-sm ms-2" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </button>
                </div>
            </form>
            
            <div class="text-center mt-4">
                <a href="#" class="text-decoration-none" data-bs-toggle="modal" data-bs-target="#forgotPasswordModal">
                    <i class="fas fa-question-circle me-1"></i>Forgot your password?
                </a>
            </div>
            
            <hr class="my-4">
            
            <div class="text-center">
                <p class="mb-2">Don't have an account?</p>
                <a href="{{ url_for('register') }}" class="btn btn-outline-primary">
                    <i class="fas fa-user-plus me-1"></i>Create Account
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Demo Section -->
<div class="row justify-content-center mt-4">
    <div class="col-lg-8">
        <div class="game-container p-4">
            <h5 class="text-center mb-3">
                <i class="fas fa-play-circle me-2"></i>New to Interactive Experiences?
            </h5>
            
            <div class="row text-center">
                <div class="col-md-3 mb-3">
                    <i class="fas fa-mouse-pointer fa-2x text-primary mb-2"></i>
                    <h6>Point & Click</h6>
                    <small class="text-muted">Simple, intuitive navigation</small>
                </div>
                
                <div class="col-md-3 mb-3">
                    <i class="fas fa-comments fa-2x text-primary mb-2"></i>
                    <h6>Interactive Dialogue</h6>
                    <small class="text-muted">Meaningful conversations</small>
                </div>
                
                <div class="col-md-3 mb-3">
                    <i class="fas fa-save fa-2x text-primary mb-2"></i>
                    <h6>Auto-Save</h6>
                    <small class="text-muted">Never lose your progress</small>
                </div>
                
                <div class="col-md-3 mb-3">
                    <i class="fas fa-mobile-alt fa-2x text-primary mb-2"></i>
                    <h6>Mobile Friendly</h6>
                    <small class="text-muted">Play anywhere, anytime</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Forgot Password Modal -->
<div class="modal fade" id="forgotPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-key me-2"></i>Reset Password
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Enter your email address and we'll send you instructions to reset your password.</p>
                
                <form id="forgotPasswordForm">
                    <div class="mb-3">
                        <label for="resetEmail" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="resetEmail" required>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>Send Reset Link
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.getElementById('loginForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        showLoading(true);
        
        try {
            const response = await fetch('/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: formData.get('username'),
                    password: formData.get('password')
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                showStatus('Login successful!', 'success');
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 1000);
            } else {
                alert(data.error || 'Login failed');
                // Clear password field on failed login
                document.getElementById('password').value = '';
            }
        } catch (error) {
            console.error('Login error:', error);
            alert('An error occurred during login');
        } finally {
            showLoading(false);
        }
    });
    
    // Forgot password form
    document.getElementById('forgotPasswordForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const email = document.getElementById('resetEmail').value;
        
        // Simulate password reset (in a real app, this would send an email)
        alert('Password reset instructions would be sent to: ' + email);
        
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('forgotPasswordModal'));
        modal.hide();
    });
    
    // Auto-focus username field
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('username').focus();
    });
    
    // Enter key handling
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            const loginForm = document.getElementById('loginForm');
            if (loginForm.contains(e.target)) {
                loginForm.dispatchEvent(new Event('submit'));
            }
        }
    });
    
    // Demo login credentials (for development)
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        const demoButton = document.createElement('button');
        demoButton.type = 'button';
        demoButton.className = 'btn btn-outline-secondary btn-sm mt-2';
        demoButton.innerHTML = '<i class="fas fa-flask me-1"></i>Demo Login';
        demoButton.onclick = function() {
            document.getElementById('username').value = 'demo';
            document.getElementById('password').value = 'demo123';
        };
        
        const loginForm = document.getElementById('loginForm');
        loginForm.appendChild(demoButton);
    }
</script>
{% endblock %}

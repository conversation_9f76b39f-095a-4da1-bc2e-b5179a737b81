from flask import Flask, render_template, request, jsonify, session, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room
from flask_login import <PERSON><PERSON><PERSON>anager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import secrets
import json
import os

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = secrets.token_hex(16)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///game.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
socketio = SocketIO(app, cors_allowed_origins="*")
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# User Model
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    age_verified = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_active = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Game progress
    current_scene = db.Column(db.String(100), default='intro')
    game_data = db.Column(db.Text, default='{}')  # JSON string for game state
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def get_game_data(self):
        return json.loads(self.game_data) if self.game_data else {}
    
    def set_game_data(self, data):
        self.game_data = json.dumps(data)

# Game Scene Model
class GameScene(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    scene_id = db.Column(db.String(100), unique=True, nullable=False)
    title = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text, nullable=False)
    choices = db.Column(db.Text, nullable=False)  # JSON string
    requirements = db.Column(db.Text, default='{}')  # JSON string for requirements
    adult_content = db.Column(db.Boolean, default=False)
    
    def get_choices(self):
        return json.loads(self.choices) if self.choices else []
    
    def get_requirements(self):
        return json.loads(self.requirements) if self.requirements else {}

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('game'))
    return render_template('index.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        data = request.get_json()
        username = data.get('username')
        email = data.get('email')
        password = data.get('password')
        age_confirmed = data.get('age_confirmed', False)
        
        # Validation
        if not all([username, email, password]):
            return jsonify({'error': 'All fields are required'}), 400
        
        if not age_confirmed:
            return jsonify({'error': 'Age verification required'}), 400
        
        if User.query.filter_by(username=username).first():
            return jsonify({'error': 'Username already exists'}), 400
        
        if User.query.filter_by(email=email).first():
            return jsonify({'error': 'Email already registered'}), 400
        
        # Create new user
        user = User(username=username, email=email, age_verified=True)
        user.set_password(password)
        db.session.add(user)
        db.session.commit()
        
        login_user(user)
        return jsonify({'success': True, 'redirect': url_for('game')})
    
    return render_template('register.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            login_user(user)
            user.last_active = datetime.utcnow()
            db.session.commit()
            return jsonify({'success': True, 'redirect': url_for('game')})
        
        return jsonify({'error': 'Invalid credentials'}), 401
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('index'))

@app.route('/game')
@login_required
def game():
    if not current_user.age_verified:
        return redirect(url_for('age_verification'))
    return render_template('game.html')

@app.route('/api/scene/<scene_id>')
@login_required
def get_scene(scene_id):
    scene = GameScene.query.filter_by(scene_id=scene_id).first()
    if not scene:
        return jsonify({'error': 'Scene not found'}), 404
    
    # Check if user meets requirements
    requirements = scene.get_requirements()
    user_data = current_user.get_game_data()
    
    for req, value in requirements.items():
        if user_data.get(req, 0) < value:
            return jsonify({'error': 'Requirements not met'}), 403
    
    return jsonify({
        'scene_id': scene.scene_id,
        'title': scene.title,
        'content': scene.content,
        'choices': scene.get_choices(),
        'adult_content': scene.adult_content
    })

@app.route('/api/choice', methods=['POST'])
@login_required
def make_choice():
    data = request.get_json()
    choice_id = data.get('choice_id')
    scene_id = data.get('scene_id')
    
    # Update user's game state
    user_data = current_user.get_game_data()
    user_data['last_choice'] = choice_id
    user_data['choices_made'] = user_data.get('choices_made', [])
    user_data['choices_made'].append({
        'scene': scene_id,
        'choice': choice_id,
        'timestamp': datetime.utcnow().isoformat()
    })
    
    current_user.set_game_data(user_data)
    db.session.commit()
    
    return jsonify({'success': True})

# WebSocket events
@socketio.on('connect')
def on_connect():
    if current_user.is_authenticated:
        join_room(f'user_{current_user.id}')
        emit('status', {'message': 'Connected to game server'})

@socketio.on('disconnect')
def on_disconnect():
    if current_user.is_authenticated:
        leave_room(f'user_{current_user.id}')

@socketio.on('game_action')
def handle_game_action(data):
    if current_user.is_authenticated:
        # Process game action
        action_type = data.get('type')
        action_data = data.get('data', {})
        
        # Emit response back to user
        emit('game_response', {
            'type': action_type,
            'data': action_data,
            'timestamp': datetime.utcnow().isoformat()
        }, room=f'user_{current_user.id}')

def init_database():
    """Initialize database with sample data"""
    db.create_all()
    
    # Create sample scenes if they don't exist
    if not GameScene.query.first():
        scenes = [
            {
                'scene_id': 'intro',
                'title': 'Welcome',
                'content': 'Welcome to the interactive experience. You are about to embark on a journey of choices and consequences.',
                'choices': json.dumps([
                    {'id': 'start', 'text': 'Begin the adventure', 'next_scene': 'chapter1'},
                    {'id': 'settings', 'text': 'Adjust preferences', 'next_scene': 'settings'}
                ]),
                'adult_content': False
            },
            {
                'scene_id': 'chapter1',
                'title': 'The Beginning',
                'content': 'Your story begins here. The choices you make will shape your experience.',
                'choices': json.dumps([
                    {'id': 'path1', 'text': 'Take the direct approach', 'next_scene': 'direct_path'},
                    {'id': 'path2', 'text': 'Choose the subtle route', 'next_scene': 'subtle_path'}
                ]),
                'adult_content': True
            }
        ]
        
        for scene_data in scenes:
            scene = GameScene(**scene_data)
            db.session.add(scene)
        
        db.session.commit()

if __name__ == '__main__':
    with app.app_context():
        init_database()
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
